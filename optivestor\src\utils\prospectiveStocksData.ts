
import { ProspectiveStock, MarketRegion } from '@/types/prospectiveStocks';

// Generate mock data for prospective stocks
const generateProspectiveStocks = (): ProspectiveStock[] => {
  const regions: MarketRegion[] = ["US", "Europe", "Asia", "Australia", "China"];
  
  const sectors = [
    "Technology", "Healthcare", "Financial Services", "Consumer Cyclical",
    "Consumer Defensive", "Industrials", "Energy", "Utilities", "Basic Materials",
    "Communication Services", "Real Estate"
  ];
  
  const industries = {
    "Technology": ["Software", "Semiconductors", "Hardware", "IT Services", "Electronic Components"],
    "Healthcare": ["Biotechnology", "Pharmaceuticals", "Medical Devices", "Healthcare Services", "Health Insurance"],
    "Financial Services": ["Banks", "Insurance", "Asset Management", "Financial Exchanges", "Credit Services"],
    "Consumer Cyclical": ["Automotive", "Retail", "Entertainment", "Restaurants", "Travel & Leisure"],
    "Consumer Defensive": ["Food", "Beverages", "Household Products", "Personal Products", "Discount Stores"],
    "Industrials": ["Aerospace & Defense", "Construction", "Machinery", "Transportation", "Business Services"],
    "Energy": ["Oil & Gas", "Renewable Energy", "Energy Equipment & Services", "Coal", "Natural Gas"],
    "Utilities": ["Electric Utilities", "Water Utilities", "Gas Utilities", "Multi-Utilities", "Independent Power Producers"],
    "Basic Materials": ["Chemicals", "Metals & Mining", "Forest Products", "Construction Materials", "Agricultural Inputs"],
    "Communication Services": ["Telecom", "Media", "Interactive Media", "Advertising", "Entertainment"],
    "Real Estate": ["REITs", "Real Estate Services", "Real Estate Development", "Property Management", "Real Estate Holdings"]
  };
  
  const marketsByRegion = {
    "US": ["NYSE", "NASDAQ", "AMEX"],
    "Europe": ["London", "Frankfurt", "Paris", "Amsterdam", "Zurich"],
    "Asia": ["Tokyo", "Hong Kong", "Singapore", "Seoul", "Mumbai"],
    "Australia": ["ASX"],
    "China": ["Shanghai", "Shenzhen"]
  };
  
  const companyPrefixes = [
    "Advanced", "Global", "International", "Strategic", "Consolidated", "Future", 
    "Premier", "Dynamic", "United", "Integrated", "Pacific", "Atlantic", "Alpine", 
    "National", "Regional", "Digital", "Smart", "Tech", "Bio", "Green", "Sustainable"
  ];
  
  const companySuffixes = [
    "Systems", "Solutions", "Technologies", "Group", "Corporation", "Inc", "Holdings", 
    "Enterprises", "Partners", "Ventures", "Industries", "Networks", "Therapeutics", 
    "Pharmaceuticals", "Energy", "Resources", "Materials", "Communications", "Healthcare"
  ];
  
  const stockList: ProspectiveStock[] = [];
  
  // Generate 200 mock stocks
  for (let i = 0; i < 200; i++) {
    const region = regions[Math.floor(Math.random() * regions.length)];
    const sector = sectors[Math.floor(Math.random() * sectors.length)];
    const industry = industries[sector][Math.floor(Math.random() * industries[sector].length)];
    const market = marketsByRegion[region][Math.floor(Math.random() * marketsByRegion[region].length)];
    
    // Generate company name
    const prefix = companyPrefixes[Math.floor(Math.random() * companyPrefixes.length)];
    const suffix = companySuffixes[Math.floor(Math.random() * companySuffixes.length)];
    const name = `${prefix} ${industry} ${suffix}`;
    
    // Generate symbol
    const symbol = prefix.substring(0, 2).toUpperCase() + industry.substring(0, 2).toUpperCase() + i.toString();
    
    // Generate scores
    const growthScore = Math.floor(Math.random() * 100) + 1;
    const valueScore = Math.floor(Math.random() * 100) + 1;
    const momentumScore = Math.floor(Math.random() * 100) + 1;
    
    // Calculate overall score with different weights
    const overallScore = Math.floor((growthScore * 0.4) + (valueScore * 0.3) + (momentumScore * 0.3));
    
    // Generate price and other metrics
    const price = Math.random() * 500 + 10;
    const change = (Math.random() * 10) - 5;
    const changePercent = (change / price) * 100;
    
    stockList.push({
      symbol,
      name,
      market,
      region,
      sector,
      industry,
      price: parseFloat(price.toFixed(2)),
      change: parseFloat(change.toFixed(2)),
      changePercent: parseFloat(changePercent.toFixed(2)),
      marketCap: parseFloat((price * (Math.random() * 100 + 1) * 1000000).toFixed(2)),
      volume: Math.floor(Math.random() * 10000000),
      pe: parseFloat((Math.random() * 50 + 5).toFixed(2)),
      eps: parseFloat((price / (Math.random() * 30 + 5)).toFixed(2)),
      dividend: parseFloat((price * Math.random() * 0.05).toFixed(2)),
      beta: parseFloat((Math.random() * 2).toFixed(2)),
      fiftyTwoWeekHigh: parseFloat((price * (1 + Math.random() * 0.3)).toFixed(2)),
      fiftyTwoWeekLow: parseFloat((price * (1 - Math.random() * 0.3)).toFixed(2)),
      analystRating: parseFloat((Math.random() * 4 + 1).toFixed(1)),
      growthScore,
      valueScore,
      momentumScore,
      overallScore
    });
  }
  
  // Sort by overall score descending
  return stockList.sort((a, b) => b.overallScore - a.overallScore);
};

export const prospectiveStocks = generateProspectiveStocks();

export const sectors = Array.from(new Set(prospectiveStocks.map(stock => stock.sector)));
export const industries = Array.from(new Set(prospectiveStocks.map(stock => stock.industry)));
export const markets = Array.from(new Set(prospectiveStocks.map(stock => stock.market)));
export const regions: MarketRegion[] = ["US", "Europe", "Asia", "Australia", "China", "Middle East", "South America", "Africa"];
