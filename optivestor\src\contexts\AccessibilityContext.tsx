
import React, { createContext, useContext, useState, useEffect } from 'react';

export type TextSize = 'small' | 'medium' | 'large' | 'extra-large';

interface AccessibilitySettings {
  textSize: TextSize;
  highContrastMode: boolean;
  screenReaderMode: boolean;
}

interface AccessibilityContextType {
  settings: AccessibilitySettings;
  updateTextSize: (size: TextSize) => void;
  toggleHighContrast: () => void;
  toggleScreenReader: () => void;
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

const defaultSettings: AccessibilitySettings = {
  textSize: 'medium',
  highContrastMode: false,
  screenReaderMode: false,
};

export function AccessibilityProvider({ children }: { children: React.ReactNode }) {
  const [settings, setSettings] = useState<AccessibilitySettings>(() => {
    const stored = localStorage.getItem('accessibility-settings');
    return stored ? JSON.parse(stored) : defaultSettings;
  });

  useEffect(() => {
    localStorage.setItem('accessibility-settings', JSON.stringify(settings));
    
    // Apply text size to document
    const root = document.documentElement;
    root.setAttribute('data-text-size', settings.textSize);
    
    // Apply high contrast mode
    if (settings.highContrastMode) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    // Apply screen reader optimizations
    if (settings.screenReaderMode) {
      root.classList.add('screen-reader-mode');
    } else {
      root.classList.remove('screen-reader-mode');
    }
  }, [settings]);

  const updateTextSize = (size: TextSize) => {
    setSettings(prev => ({ ...prev, textSize: size }));
  };

  const toggleHighContrast = () => {
    setSettings(prev => ({ ...prev, highContrastMode: !prev.highContrastMode }));
  };

  const toggleScreenReader = () => {
    setSettings(prev => ({ ...prev, screenReaderMode: !prev.screenReaderMode }));
  };

  return (
    <AccessibilityContext.Provider value={{
      settings,
      updateTextSize,
      toggleHighContrast,
      toggleScreenReader,
    }}>
      {children}
    </AccessibilityContext.Provider>
  );
}

export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
}
