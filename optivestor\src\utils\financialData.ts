import type { 
  FinancialData, BalanceSheetData, CashFlowData, 
  ValuationMetrics, StockPrediction, NewsItem 
} from '@/types/stock';

// Mock financial data
export const mockFinancialData: FinancialData[] = [
  {
    symbol: 'AAPL',
    period: '2023',
    revenue: 394328000000,
    netIncome: 96995000000,
    ebitda: 130541000000,
    grossMargin: 44.3,
    operatingMargin: 29.8,
    netMargin: 24.6,
    eps: 6.14
  },
  {
    symbol: 'AAPL',
    period: '2022',
    revenue: 394328000000,
    netIncome: 99803000000,
    ebitda: 130538000000,
    grossMargin: 43.3,
    operatingMargin: 30.3,
    netMargin: 25.3,
    eps: 6.11
  },
  {
    symbol: 'AAPL',
    period: '2021',
    revenue: 365817000000,
    netIncome: 94680000000,
    ebitda: 120233000000,
    grossMargin: 41.8,
    operatingMargin: 29.8,
    netMargin: 25.9,
    eps: 5.61
  },
  {
    symbol: 'AAPL',
    period: '2020',
    revenue: 274515000000,
    netIncome: 57411000000,
    ebitda: 81452000000,
    grossMargin: 38.2,
    operatingMargin: 24.1,
    netMargin: 20.9,
    eps: 3.28
  }
];

// Mock balance sheet data
export const mockBalanceSheetData: BalanceSheetData[] = [
  {
    symbol: 'AAPL',
    period: '2023',
    totalAssets: 352583000000,
    totalLiabilities: 290468000000,
    totalEquity: 62115000000,
    cashAndEquivalents: 29965000000,
    shortTermDebt: 10628000000,
    longTermDebt: 95281000000,
    debtToEquityRatio: 1.7
  },
  {
    symbol: 'AAPL',
    period: '2022',
    totalAssets: 352755000000,
    totalLiabilities: 302083000000,
    totalEquity: 50672000000,
    cashAndEquivalents: 23646000000,
    shortTermDebt: 11129000000,
    longTermDebt: 98959000000,
    debtToEquityRatio: 2.2
  }
];

// Mock cash flow data
export const mockCashFlowData: CashFlowData[] = [
  {
    symbol: 'AAPL',
    period: '2023',
    operatingCashFlow: 113761000000,
    investingCashFlow: -10744000000,
    financingCashFlow: -96003000000,
    freeCashFlow: 99573000000,
    capex: -11077000000,
    dividendsPaid: -14932000000
  },
  {
    symbol: 'AAPL',
    period: '2022',
    operatingCashFlow: 122151000000,
    investingCashFlow: 6076000000,
    financingCashFlow: -110749000000,
    freeCashFlow: 111443000000,
    capex: -10708000000,
    dividendsPaid: -14789000000
  }
];

// Mock valuation metrics
export const mockValuationMetrics: ValuationMetrics[] = [
  {
    symbol: 'AAPL',
    pe: 28.5,
    pegRatio: 2.4,
    pb: 32.8,
    ps: 7.5,
    dividendYield: 0.51,
    payoutRatio: 15.6
  },
  {
    symbol: 'MSFT',
    pe: 32.8,
    pegRatio: 2.0,
    pb: 11.2,
    ps: 12.3,
    dividendYield: 0.68,
    payoutRatio: 23.5
  }
];

// Mock price prediction data with confidence intervals
export const mockPredictions: StockPrediction[] = [
  {
    symbol: 'AAPL',
    date: '2024-05',
    predictedPrice: 192.45,
    lowerBound: 185.70,
    upperBound: 199.20,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2024-06',
    predictedPrice: 196.82,
    lowerBound: 187.25,
    upperBound: 206.39,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2024-07',
    predictedPrice: 201.35,
    lowerBound: 189.67,
    upperBound: 213.03,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2024-08',
    predictedPrice: 205.58,
    lowerBound: 191.19,
    upperBound: 219.97,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2024-09',
    predictedPrice: 209.54,
    lowerBound: 192.78,
    upperBound: 226.30,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2024-10',
    predictedPrice: 213.45,
    lowerBound: 194.24,
    upperBound: 232.66,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2024-11',
    predictedPrice: 217.83,
    lowerBound: 196.12,
    upperBound: 239.54,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2024-12',
    predictedPrice: 223.15,
    lowerBound: 199.61,
    upperBound: 246.69,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2025-01',
    predictedPrice: 227.42,
    lowerBound: 201.33,
    upperBound: 253.51,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2025-02',
    predictedPrice: 232.18,
    lowerBound: 203.65,
    upperBound: 260.71,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2025-03',
    predictedPrice: 238.51,
    lowerBound: 207.50,
    upperBound: 269.52,
    confidenceInterval: 0.80
  },
  {
    symbol: 'AAPL',
    date: '2025-04',
    predictedPrice: 243.64,
    lowerBound: 210.22,
    upperBound: 277.06,
    confidenceInterval: 0.80
  }
];

// Generate mock news items with correct sentiment type
export const mockNews: NewsItem[] = [
  {
    id: '1',
    title: 'Federal Reserve Signals Potential Rate Cuts Later This Year',
    summary: 'The Federal Reserve indicated it may begin cutting interest rates later this year if inflation continues to moderate, according to minutes from the recent FOMC meeting.',
    source: 'Financial Times',
    url: '#',
    publishedAt: new Date(Date.now() - 3600000 * 2),
    date: new Date(Date.now() - 3600000 * 2).toISOString().slice(0, 10),
    relatedSymbols: ['SPX', 'DJI'],
    sentiment: 'positive'
  },
  {
    id: '2',
    title: 'Apple Announces New AI Features for iPhone',
    summary: 'Apple unveiled new AI capabilities for the upcoming iPhone models at its annual developer conference, highlighting privacy-focused on-device processing.',
    source: 'Tech Insider',
    url: '#',
    imageUrl: 'https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?q=80&w=1470&auto=format&fit=crop',
    publishedAt: new Date(Date.now() - 3600000 * 5),
    date: new Date(Date.now() - 3600000 * 5).toISOString().slice(0, 10),
    relatedSymbols: ['AAPL'],
    sentiment: 'positive'
  },
  {
    id: '3',
    title: 'NVIDIA Surpasses $2 Trillion Market Cap on AI Chip Demand',
    summary: 'NVIDIA\'s stock reached new heights, pushing its market cap above $2 trillion as demand for AI chips continues to exceed expectations.',
    source: 'Market Watch',
    url: '#',
    publishedAt: new Date(Date.now() - 3600000 * 8),
    date: new Date(Date.now() - 3600000 * 8).toISOString().slice(0, 10),
    relatedSymbols: ['NVDA'],
    sentiment: 'positive'
  },
  {
    id: '4',
    title: 'Oil Prices Drop Amid Concerns of Slowing Global Demand',
    summary: 'Crude oil prices fell more than 2% on Thursday as investors weighed reports suggesting slower-than-expected global economic growth.',
    source: 'Energy Report',
    url: '#',
    publishedAt: new Date(Date.now() - 3600000 * 10),
    date: new Date(Date.now() - 3600000 * 10).toISOString().slice(0, 10),
    sentiment: 'negative'
  },
  {
    id: '5',
    title: 'Tesla Deliveries Beat Estimates Despite EV Market Slowdown',
    summary: 'Tesla reported quarterly deliveries that exceeded analyst expectations, bucking the trend of a broader slowdown in electric vehicle sales.',
    source: 'Auto Insights',
    url: '#',
    imageUrl: 'https://images.unsplash.com/photo-1617788138017-80ad40651399?q=80&w=1632&auto=format&fit=crop',
    publishedAt: new Date(Date.now() - 3600000 * 12),
    date: new Date(Date.now() - 3600000 * 12).toISOString().slice(0, 10),
    relatedSymbols: ['TSLA'],
    sentiment: 'positive'
  }
];

export function getFinancialData(symbol: string): FinancialData[] {
  return mockFinancialData.filter(data => data.symbol === symbol);
}

export function getBalanceSheetData(symbol: string): BalanceSheetData[] {
  return mockBalanceSheetData.filter(data => data.symbol === symbol);
}

export function getCashFlowData(symbol: string): CashFlowData[] {
  return mockCashFlowData.filter(data => data.symbol === symbol);
}

export function getValuationMetrics(symbol: string): ValuationMetrics | undefined {
  return mockValuationMetrics.find(data => data.symbol === symbol);
}

export function getPredictions(symbol: string): StockPrediction[] {
  return mockPredictions.filter(prediction => prediction.symbol === symbol);
}
