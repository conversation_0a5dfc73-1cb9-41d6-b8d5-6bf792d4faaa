
import React from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, 
  ResponsiveContainer, BarChart, Bar, Cell 
} from 'recharts';

const Markets = () => {
  // Mock market indices data
  const marketIndices = [
    { name: 'S&P 500', value: 5134.27, change: 0.68, symbol: 'SPX' },
    { name: '<PERSON>', value: 38239.98, change: 0.33, symbol: 'DJI' },
    { name: 'Nasdaq', value: 16780.30, change: 1.10, symbol: 'COMP' },
    { name: 'Russell 2000', value: 2054.95, change: -0.21, symbol: 'RUT' },
    { name: 'VIX', value: 14.75, change: -3.12, symbol: 'VIX' }
  ];
  
  // Mock sector performance
  const sectorPerformance = [
    { name: 'Technology', change: 1.5 },
    { name: 'Healthcare', change: -0.3 },
    { name: 'Financial', change: 0.2 },
    { name: 'Consumer', change: 0.7 },
    { name: 'Energy', change: -0.8 },
    { name: 'Industrial', change: 0.5 },
    { name: 'Materials', change: -0.4 },
    { name: 'Utilities', change: -0.2 },
    { name: 'Real Estate', change: 0.1 },
    { name: 'Communication', change: 1.2 }
  ].sort((a, b) => b.change - a.change);
  
  // Generate mock market index chart data
  const generateMarketData = () => {
    const data = [];
    const now = new Date();
    for (let i = 30; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      data.push({
        date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        sp500: 5134 - Math.random() * 300,
        nasdaq: 16780 - Math.random() * 800,
        djia: 38240 - Math.random() * 1500
      });
    }
    return data;
  };
  
  const marketData = generateMarketData();
  
  return (
    <PageLayout title="Markets Overview">
      <div className="grid grid-cols-1 gap-6">
        {/* Market Indices */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {marketIndices.map((index) => (
            <Card key={index.symbol}>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium">{index.name}</p>
                    <p className="text-xs text-muted-foreground">{index.symbol}</p>
                  </div>
                  <Badge 
                    variant={index.change >= 0 ? "default" : "destructive"}
                    className="ml-auto"
                  >
                    {index.change >= 0 ? '+' : ''}{index.change.toFixed(2)}%
                  </Badge>
                </div>
                <p className="text-2xl font-bold mt-2">{index.value.toLocaleString(undefined, { maximumFractionDigits: 2 })}</p>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Market Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Major Indices Performance</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={marketData}
                margin={{ top: 10, right: 10, left: 0, bottom: 0 }}
              >
                <defs>
                  <linearGradient id="colorSP500" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0} />
                  </linearGradient>
                  <linearGradient id="colorNasdaq" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="hsl(var(--success))" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="hsl(var(--success))" stopOpacity={0} />
                  </linearGradient>
                  <linearGradient id="colorDJIA" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="hsl(var(--warning))" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="hsl(var(--warning))" stopOpacity={0} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="hsl(var(--border))" />
                <XAxis 
                  dataKey="date" 
                  tick={{ fontSize: 10 }}
                  tickMargin={10}
                />
                <YAxis 
                  tick={{ fontSize: 10 }}
                  tickMargin={10}
                  domain={['auto', 'auto']}
                  tickFormatter={(value) => `${(value / 1000).toFixed(1)}K`}
                />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="sp500" 
                  name="S&P 500" 
                  stroke="hsl(var(--primary))" 
                  fillOpacity={1}
                  fill="url(#colorSP500)"
                />
                <Area 
                  type="monotone" 
                  dataKey="nasdaq" 
                  name="NASDAQ" 
                  stroke="hsl(var(--success))" 
                  fillOpacity={1}
                  fill="url(#colorNasdaq)"
                />
                <Area 
                  type="monotone" 
                  dataKey="djia" 
                  name="Dow Jones" 
                  stroke="hsl(var(--warning))" 
                  fillOpacity={1}
                  fill="url(#colorDJIA)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        {/* Sector Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Sector Performance</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={sectorPerformance}
                layout="vertical"
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                <XAxis
                  type="number"
                  tickFormatter={(value) => `${value > 0 ? '+' : ''}${value}%`}
                />
                <YAxis 
                  dataKey="name" 
                  type="category" 
                  width={100}
                />
                <Tooltip 
                  formatter={(value) => [`${Number(value) > 0 ? '+' : ''}${value}%`, 'Change']}
                />
                <Bar dataKey="change" radius={[0, 4, 4, 0]}>
                  {sectorPerformance.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={Number(entry.change) > 0 ? 'hsl(var(--success))' : 'hsl(var(--danger))'} 
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </PageLayout>
  );
};

export default Markets;
