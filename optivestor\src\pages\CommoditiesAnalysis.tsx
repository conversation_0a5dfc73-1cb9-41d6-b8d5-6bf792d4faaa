
import { useState } from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { CommodityFilter } from '@/components/commodities/CommodityFilter';
import { CommodityCard } from '@/components/commodities/CommodityCard';
import { CommodityDetails } from '@/components/commodities/CommodityDetails';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CommodityFilterOptions, Commodity } from '@/types/commodities';
import { allCommodities } from '@/utils/commoditiesData';

const CommoditiesAnalysis = () => {
  const [selectedCommodity, setSelectedCommodity] = useState<Commodity | null>(null);
  const [filteredCommodities, setFilteredCommodities] = useState<Commodity[]>(
    [...allCommodities].sort((a, b) => b.overallScore - a.overallScore).slice(0, 50)
  );
  const [activeTab, setActiveTab] = useState<string>("all");
  
  const handleFilterChange = (filters: CommodityFilterOptions) => {
    let filtered = [...allCommodities];
    
    // Filter by category
    if (filters.categories && filters.categories.length > 0) {
      filtered = filtered.filter(commodity => 
        filters.categories!.includes(commodity.category)
      );
    }
    
    // Filter by risk level
    if (filters.maxRiskLevel) {
      const riskLevels = ["Low", "Moderate", "High", "Very High"];
      const maxRiskIndex = riskLevels.indexOf(filters.maxRiskLevel);
      
      filtered = filtered.filter(commodity => {
        const commodityRiskIndex = riskLevels.indexOf(commodity.riskLevel);
        return commodityRiskIndex <= maxRiskIndex;
      });
    }
    
    // Filter by minimum overall score
    if (filters.minOverallScore) {
      filtered = filtered.filter(commodity => 
        commodity.overallScore >= filters.minOverallScore!
      );
    }
    
    // Filter by minimum risk/benefit ratio
    if (filters.minRiskBenefitRatio) {
      filtered = filtered.filter(commodity => 
        commodity.riskBenefitRatio >= filters.minRiskBenefitRatio!
      );
    }
    
    // Filter by investment horizons
    if (filters.investmentHorizons && filters.investmentHorizons.length > 0) {
      filtered = filtered.filter(commodity => 
        filters.investmentHorizons!.includes(commodity.investmentHorizon)
      );
    }
    
    // Sort by overall score and get top 50
    const sorted = filtered.sort((a, b) => b.overallScore - a.overallScore);
    setFilteredCommodities(sorted.slice(0, 50));
    setSelectedCommodity(null);
  };
  
  const handleCommoditySelect = (commodity: Commodity) => {
    setSelectedCommodity(commodity);
  };
  
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    
    let filtered = [...allCommodities];
    
    if (value !== "all") {
      filtered = filtered.filter(commodity => commodity.category === value);
    }
    
    const sorted = filtered.sort((a, b) => b.overallScore - a.overallScore);
    setFilteredCommodities(sorted.slice(0, 50));
    setSelectedCommodity(null);
  };

  return (
    <PageLayout title="Commodities Analysis">
      <div className="space-y-6">
        <CommodityFilter onFilterChange={handleFilterChange} />
        
        <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="mb-4 flex flex-wrap">
            <TabsTrigger value="all">All Commodities</TabsTrigger>
            <TabsTrigger value="Energy">Energy</TabsTrigger>
            <TabsTrigger value="Precious Metals">Precious Metals</TabsTrigger>
            <TabsTrigger value="Industrial Metals">Industrial Metals</TabsTrigger>
            <TabsTrigger value="Agriculture">Agriculture</TabsTrigger>
            <TabsTrigger value="Livestock">Livestock</TabsTrigger>
            <TabsTrigger value="Softs">Softs</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {filteredCommodities.map(commodity => (
                    <CommodityCard 
                      key={commodity.id}
                      commodity={commodity}
                      onClick={handleCommoditySelect}
                      isSelected={selectedCommodity?.id === commodity.id}
                    />
                  ))}
                </div>
              </div>
              <div className="lg:col-span-1">
                <CommodityDetails commodity={selectedCommodity} />
              </div>
            </div>
          </TabsContent>
          
          {["Energy", "Precious Metals", "Industrial Metals", "Agriculture", "Livestock", "Softs"].map(category => (
            <TabsContent key={category} value={category}>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {filteredCommodities.map(commodity => (
                      <CommodityCard 
                        key={commodity.id}
                        commodity={commodity}
                        onClick={handleCommoditySelect}
                        isSelected={selectedCommodity?.id === commodity.id}
                      />
                    ))}
                  </div>
                </div>
                <div className="lg:col-span-1">
                  <CommodityDetails commodity={selectedCommodity} />
                </div>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </PageLayout>
  );
};

export default CommoditiesAnalysis;
