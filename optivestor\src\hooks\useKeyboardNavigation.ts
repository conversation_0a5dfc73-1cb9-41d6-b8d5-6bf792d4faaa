
import { useEffect, useCallback, useRef } from 'react';

interface KeyboardNavigationOptions {
  enableArrowKeys?: boolean;
  enableTabNavigation?: boolean;
  enableEscapeKey?: boolean;
  onEscape?: () => void;
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  onEnter?: () => void;
  onSpace?: () => void;
}

export function useKeyboardNavigation({
  enableArrowKeys = true,
  enableTabNavigation = true,
  enableEscapeKey = true,
  onEscape,
  onArrowUp,
  onArrowDown,
  onArrowLeft,
  onArrowRight,
  onEnter,
  onSpace
}: KeyboardNavigationOptions = {}) {
  const isListening = useRef(false);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isListening.current) return;

    switch (event.key) {
      case 'Escape':
        if (enableEscapeKey && onEscape) {
          event.preventDefault();
          onEscape();
        }
        break;
      case 'ArrowUp':
        if (enableArrowKeys && onArrowUp) {
          event.preventDefault();
          onArrowUp();
        }
        break;
      case 'ArrowDown':
        if (enableArrowKeys && onArrowDown) {
          event.preventDefault();
          onArrowDown();
        }
        break;
      case 'ArrowLeft':
        if (enableArrowKeys && onArrowLeft) {
          event.preventDefault();
          onArrowLeft();
        }
        break;
      case 'ArrowRight':
        if (enableArrowKeys && onArrowRight) {
          event.preventDefault();
          onArrowRight();
        }
        break;
      case 'Enter':
        if (onEnter) {
          event.preventDefault();
          onEnter();
        }
        break;
      case ' ':
        if (onSpace) {
          event.preventDefault();
          onSpace();
        }
        break;
    }
  }, [
    enableArrowKeys,
    enableTabNavigation,
    enableEscapeKey,
    onEscape,
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onEnter,
    onSpace
  ]);

  const startListening = useCallback(() => {
    isListening.current = true;
    document.addEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  const stopListening = useCallback(() => {
    isListening.current = false;
    document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  useEffect(() => {
    return () => {
      stopListening();
    };
  }, [stopListening]);

  return {
    startListening,
    stopListening,
    isListening: () => isListening.current
  };
}
