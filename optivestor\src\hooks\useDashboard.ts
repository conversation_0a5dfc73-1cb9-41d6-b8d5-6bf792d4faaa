
import { useState, useEffect } from 'react';
import { useStockData } from '@/utils/stockApi';
import { getDashboardData } from '@/services/dataService';
import { useWatchlist } from './useWatchlist';
import { useStockSelection } from './useStockSelection';
import type { MarketStatsData } from '@/components/dashboard/MarketStats';

interface DashboardState {
  isLoading: boolean;
  error: string | null;
}

interface DashboardData {
  marketStats: MarketStatsData;
  news: any[];
  predictions: any[];
  historicalPrices: Array<{ date: string; price: number }>;
}

export const useDashboard = () => {
  // Get stocks data
  const stocks = useStockData();

  // Use focused hooks for specific responsibilities
  const stockSelection = useStockSelection({
    defaultSymbol: 'AAPL',
    stocks
  });

  const watchlist = useWatchlist({
    initialStocks: stocks && stocks.length > 0 ? stocks.slice(0, 3) : [],
    maxItems: 10
  });

  // Simplified dashboard state
  const [state, setState] = useState<DashboardState>({
    isLoading: true,
    error: null
  });

  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  
  // Main data fetching effect
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setState(prev => ({ ...prev, isLoading: true, error: null }));

        // Only proceed if we have stocks data and a valid selection
        if (!stocks || stocks.length === 0 || !stockSelection.hasValidSelection) {
          console.log('Waiting for stocks data or valid selection...');
          setState(prev => ({ ...prev, isLoading: false }));
          return;
        }

        const data = getDashboardData(stockSelection.selectedStockSymbol, stocks);
        setDashboardData(data);
        setState(prev => ({ ...prev, isLoading: false }));
      } catch (error) {
        console.error('Dashboard data fetch error:', error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to load dashboard data'
        }));
      }
    };

    fetchDashboardData();
  }, [stockSelection.selectedStockSymbol, stocks, stockSelection.hasValidSelection]);
  
  // Simplified handler functions that delegate to focused hooks
  const handleSearch = (term: string) => {
    const success = stockSelection.searchAndSelectStock(term);
    if (!success) {
      console.log(`No stock found for search term: ${term}`);
    }
  };

  const handleAddToWatchlist = () => {
    if (stockSelection.selectedStock) {
      watchlist.addToWatchlist(stockSelection.selectedStock);
    }
  };

  const handleRemoveFromWatchlist = (symbol: string) => {
    watchlist.removeFromWatchlist(symbol);
  };

  const handleSelectWatchlistStock = (symbol: string) => {
    stockSelection.selectStock(symbol);
  };

  return {
    // Stock data
    stocks,
    selectedStock: stockSelection.selectedStock,
    selectedStockSymbol: stockSelection.selectedStockSymbol,
    setSelectedStockSymbol: stockSelection.selectStock,

    // Watchlist data
    watchlistStocks: watchlist.watchlistStocks,

    // Dashboard data
    dashboardData,
    isLoading: state.isLoading,
    error: state.error,

    // Handler functions
    handleSearch,
    handleAddToWatchlist,
    handleRemoveFromWatchlist,
    handleSelectWatchlistStock,

    // Additional utilities
    selectNextStock: stockSelection.selectNextStock,
    selectPreviousStock: stockSelection.selectPreviousStock,
    isInWatchlist: watchlist.isInWatchlist,
    clearWatchlist: watchlist.clearWatchlist
  };
};

export type { DashboardState, DashboardData };
