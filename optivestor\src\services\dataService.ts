
import { mockNews, getPredictions, generatePriceHistory } from '@/utils/stockApi';
import type { Stock } from '@/utils/stockApi';
import type { MarketStatsData } from '@/components/dashboard/MarketStats';

interface DashboardServiceData {
  news: typeof mockNews;
  marketStats: MarketStatsData;
  predictions: ReturnType<typeof getPredictions>;
  historicalPrices: Array<{ date: string; price: number }>;
}

export const getMarketStats = (): MarketStatsData => {
  try {
    return {
      totalMarketCap: "$42.18T",
      tradingVolume: "2.64B",
      topGainer: {
        symbol: "NVDA",
        name: "NVIDIA Corp",
        change: 2.01
      },
      topLoser: {
        symbol: "TSLA", 
        name: "Tesla Inc",
        change: -1.35
      }
    };
  } catch (error) {
    console.error('Error getting market stats:', error);
    throw new Error('Failed to fetch market statistics');
  }
};

export const getDashboardData = (selectedSymbol: string, stocks: Stock[]): DashboardServiceData => {
  try {
    if (!selectedSymbol) {
      throw new Error('Selected symbol is required');
    }

    if (!stocks || stocks.length === 0) {
      throw new Error('No stocks data available');
    }

    const selectedStock = stocks.find(stock => stock.symbol === selectedSymbol) || stocks[0];

    if (!selectedStock) {
      throw new Error(`Stock with symbol ${selectedSymbol} not found`);
    }

    // Generate historical prices using the simple approach
    const daysOfHistory = 30;
    const historicalPrices = Array.from({ length: daysOfHistory }, (_, index) => {
      const date = new Date();
      date.setDate(date.getDate() - (daysOfHistory - index));
      const basePrice = selectedStock.price;
      const variation = (Math.random() - 0.5) * 0.1 * basePrice;
      return {
        date: date.toISOString().slice(0, 10),
        price: parseFloat((basePrice + variation).toFixed(2))
      };
    });

    return {
      news: mockNews.slice(0, 5),
      marketStats: getMarketStats(),
      predictions: getPredictions(selectedSymbol),
      historicalPrices
    };
  } catch (error) {
    console.error('Error getting dashboard data:', error);
    throw error;
  }
};

export { mockNews, getPredictions, generatePriceHistory };
export type { MarketStatsData, DashboardServiceData };
