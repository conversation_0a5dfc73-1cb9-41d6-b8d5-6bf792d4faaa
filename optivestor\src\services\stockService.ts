import { Stock, generatePriceHistory } from '@/utils/stockApi';

export interface StockServiceOptions {
  symbol?: string;
  includeHistory?: boolean;
  historyDays?: number;
}

export class StockService {
  private static instance: StockService;

  public static getInstance(): StockService {
    if (!StockService.instance) {
      StockService.instance = new StockService();
    }
    return StockService.instance;
  }

  /**
   * Get stock data with optional historical prices
   */
  async getStockData(stocks: Stock[], options: StockServiceOptions = {}) {
    try {
      const { symbol, includeHistory = false, historyDays = 30 } = options;

      let targetStock: Stock | undefined;
      
      if (symbol) {
        targetStock = stocks.find(stock => stock.symbol === symbol);
        if (!targetStock) {
          throw new Error(`Stock with symbol ${symbol} not found`);
        }
      }

      const result = {
        stock: targetStock,
        stocks,
        historicalPrices: includeHistory && targetStock 
          ? this.generateHistoricalPrices(targetStock, historyDays)
          : null
      };

      return result;
    } catch (error) {
      console.error('Error in StockService.getStockData:', error);
      throw error;
    }
  }

  /**
   * Search stocks by symbol or name
   */
  searchStocks(stocks: Stock[], searchTerm: string): Stock[] {
    try {
      if (!searchTerm.trim()) {
        return stocks;
      }

      const term = searchTerm.toLowerCase();
      return stocks.filter(stock => 
        stock.symbol.toLowerCase().includes(term) ||
        stock.name.toLowerCase().includes(term)
      );
    } catch (error) {
      console.error('Error in StockService.searchStocks:', error);
      return [];
    }
  }

  /**
   * Get top performing stocks
   */
  getTopPerformers(stocks: Stock[], limit: number = 5): Stock[] {
    try {
      return [...stocks]
        .sort((a, b) => b.changePercent - a.changePercent)
        .slice(0, limit);
    } catch (error) {
      console.error('Error in StockService.getTopPerformers:', error);
      return [];
    }
  }

  /**
   * Get worst performing stocks
   */
  getWorstPerformers(stocks: Stock[], limit: number = 5): Stock[] {
    try {
      return [...stocks]
        .sort((a, b) => a.changePercent - b.changePercent)
        .slice(0, limit);
    } catch (error) {
      console.error('Error in StockService.getWorstPerformers:', error);
      return [];
    }
  }

  /**
   * Get stocks by market cap range
   */
  getStocksByMarketCap(stocks: Stock[], minCap: number, maxCap: number): Stock[] {
    try {
      return stocks.filter(stock => 
        stock.marketCap >= minCap && stock.marketCap <= maxCap
      );
    } catch (error) {
      console.error('Error in StockService.getStocksByMarketCap:', error);
      return [];
    }
  }

  /**
   * Generate historical price data for a stock
   */
  private generateHistoricalPrices(stock: Stock, days: number) {
    try {
      const prices = generatePriceHistory(days, stock.price, 2);
      return prices.map((price, index) => {
        const date = new Date();
        date.setDate(date.getDate() - (days - index));
        return {
          date: date.toISOString().slice(0, 10),
          price: parseFloat(price.toFixed(2))
        };
      });
    } catch (error) {
      console.error('Error generating historical prices:', error);
      return [];
    }
  }

  /**
   * Calculate portfolio metrics
   */
  calculatePortfolioMetrics(stocks: Stock[]) {
    try {
      if (!stocks || stocks.length === 0) {
        return {
          totalValue: 0,
          totalChange: 0,
          totalChangePercent: 0,
          gainers: 0,
          losers: 0
        };
      }

      const totalValue = stocks.reduce((sum, stock) => sum + stock.marketCap, 0);
      const totalChange = stocks.reduce((sum, stock) => sum + stock.change, 0);
      const gainers = stocks.filter(stock => stock.change > 0).length;
      const losers = stocks.filter(stock => stock.change < 0).length;
      
      // Calculate weighted average change percentage
      const totalChangePercent = stocks.reduce((sum, stock) => {
        const weight = stock.marketCap / totalValue;
        return sum + (stock.changePercent * weight);
      }, 0);

      return {
        totalValue,
        totalChange,
        totalChangePercent,
        gainers,
        losers
      };
    } catch (error) {
      console.error('Error calculating portfolio metrics:', error);
      return {
        totalValue: 0,
        totalChange: 0,
        totalChangePercent: 0,
        gainers: 0,
        losers: 0
      };
    }
  }
}

// Export singleton instance
export const stockService = StockService.getInstance();
