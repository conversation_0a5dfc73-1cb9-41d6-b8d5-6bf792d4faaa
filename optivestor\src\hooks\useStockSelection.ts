import { useState, useMemo } from 'react';
import { Stock } from '@/utils/stockApi';

interface UseStockSelectionOptions {
  defaultSymbol?: string;
  stocks: Stock[];
}

export const useStockSelection = (options: UseStockSelectionOptions) => {
  const { defaultSymbol = 'AAPL', stocks } = options;
  
  const [selectedStockSymbol, setSelectedStockSymbol] = useState<string>(defaultSymbol);

  // Memoize selected stock to avoid unnecessary recalculations
  const selectedStock = useMemo(() => {
    if (!stocks || stocks.length === 0) {
      return null;
    }
    return stocks.find(stock => stock.symbol === selectedStockSymbol) || stocks[0];
  }, [stocks, selectedStockSymbol]);

  const selectStock = (symbol: string) => {
    try {
      // Validate that the stock exists
      const stockExists = stocks.some(stock => stock.symbol === symbol);
      if (!stockExists) {
        console.warn(`Stock with symbol ${symbol} not found`);
        return false;
      }
      
      setSelectedStockSymbol(symbol);
      return true;
    } catch (error) {
      console.error('Error selecting stock:', error);
      return false;
    }
  };

  const searchAndSelectStock = (searchTerm: string): boolean => {
    try {
      if (!searchTerm.trim()) {
        return false;
      }

      const foundStock = stocks.find(stock => 
        stock.symbol.toLowerCase() === searchTerm.toLowerCase() || 
        stock.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      if (foundStock) {
        setSelectedStockSymbol(foundStock.symbol);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Search error:', error);
      return false;
    }
  };

  const selectNextStock = () => {
    try {
      if (!stocks || stocks.length === 0) return false;
      
      const currentIndex = stocks.findIndex(stock => stock.symbol === selectedStockSymbol);
      const nextIndex = (currentIndex + 1) % stocks.length;
      setSelectedStockSymbol(stocks[nextIndex].symbol);
      return true;
    } catch (error) {
      console.error('Error selecting next stock:', error);
      return false;
    }
  };

  const selectPreviousStock = () => {
    try {
      if (!stocks || stocks.length === 0) return false;
      
      const currentIndex = stocks.findIndex(stock => stock.symbol === selectedStockSymbol);
      const prevIndex = currentIndex <= 0 ? stocks.length - 1 : currentIndex - 1;
      setSelectedStockSymbol(stocks[prevIndex].symbol);
      return true;
    } catch (error) {
      console.error('Error selecting previous stock:', error);
      return false;
    }
  };

  return {
    selectedStock,
    selectedStockSymbol,
    selectStock,
    searchAndSelectStock,
    selectNextStock,
    selectPreviousStock,
    hasValidSelection: selectedStock !== null
  };
};

export type { UseStockSelectionOptions };
