
import { useSearchParams } from 'react-router-dom';
import { useState } from 'react';
import { useStockData, getFinancialData, getBalanceSheetData, getCashFlowData } from '@/utils/stockApi';

export const useFinancialData = () => {
  const [searchParams] = useSearchParams();
  const urlSymbol = searchParams.get('symbol');
  const [selectedStockSymbol, setSelectedStockSymbol] = useState(urlSymbol || 'AAPL');
  
  const stocks = useStockData();
  const selectedStock = stocks.find(stock => stock.symbol === selectedStockSymbol) || stocks[0];
  
  const financialData = getFinancialData(selectedStock.symbol);
  const balanceSheetData = getBalanceSheetData(selectedStock.symbol);
  const cashFlowData = getCashFlowData(selectedStock.symbol);

  const handleSearch = (term: string) => {
    const foundStock = stocks.find(stock => 
      stock.symbol.toLowerCase() === term.toLowerCase() || 
      stock.name.toLowerCase().includes(term.toLowerCase())
    );
    
    if (foundStock) {
      setSelectedStockSymbol(foundStock.symbol);
    }
  };

  return {
    selectedStock,
    financialData,
    balanceSheetData,
    cashFlowData,
    handleSearch
  };
};
