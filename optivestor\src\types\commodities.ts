
export interface Commodity {
  id: string;
  name: string;
  symbol: string;
  category: CommodityCategory;
  currentPrice: number;
  changePercent: number;
  change: number;
  currency: string;
  unit: string;
  volume: number;
  openInterest: number;
  yearToDateReturn: number;
  yearHigh: number;
  yearLow: number;
  volatility: number; // Historical volatility (%)
  technicalScore: number; // Technical analysis score (0-100)
  fundamentalScore: number; // Fundamental analysis score (0-100)
  sentimentScore: number; // Market sentiment score (0-100)
  overallScore: number; // Combined score (0-100)
  riskLevel: RiskLevel; // Risk assessment
  riskBenefitRatio: number; // Expected return / risk ratio
  correlationWithSP500: number; // -1 to 1
  inflationHedgeScore: number; // How well it hedges inflation (0-100)
  recessionResistanceScore: number; // How well it performs in recession (0-100)
  sustainabilityScore?: number; // ESG score if applicable (0-100)
  investmentHorizon: InvestmentHorizon; // Recommended investment timeframe
}

export type CommodityCategory = 
  | "Energy" 
  | "Precious Metals" 
  | "Industrial Metals" 
  | "Agriculture" 
  | "Livestock" 
  | "Softs";

export type RiskLevel = "Low" | "Moderate" | "High" | "Very High";

export type InvestmentHorizon = "Short-term" | "Medium-term" | "Long-term";

export interface CommodityFilterOptions {
  categories?: CommodityCategory[];
  minOverallScore?: number;
  maxRiskLevel?: RiskLevel;
  minRiskBenefitRatio?: number;
  investmentHorizons?: InvestmentHorizon[];
}
