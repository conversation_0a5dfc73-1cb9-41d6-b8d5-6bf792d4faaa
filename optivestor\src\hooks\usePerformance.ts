
import { useCallback, useMemo, useRef } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

interface UsePerformanceOptions {
  cacheExpiry?: number; // in milliseconds
  debounceDelay?: number;
}

export function usePerformance<T>({
  cacheExpiry = 5 * 60 * 1000, // 5 minutes default
  debounceDelay = 300
}: UsePerformanceOptions = {}) {
  const cache = useRef<Map<string, CacheEntry<T>>>(new Map());
  const debounceTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const memoizedCache = useMemo(() => {
    const setCache = (key: string, data: T) => {
      cache.current.set(key, {
        data,
        timestamp: Date.now(),
        expiry: Date.now() + cacheExpiry
      });
    };

    const getCache = (key: string): T | null => {
      const entry = cache.current.get(key);
      if (!entry) return null;
      
      if (Date.now() > entry.expiry) {
        cache.current.delete(key);
        return null;
      }
      
      return entry.data;
    };

    const clearCache = () => {
      cache.current.clear();
    };

    const clearExpiredCache = () => {
      const now = Date.now();
      for (const [key, entry] of cache.current.entries()) {
        if (now > entry.expiry) {
          cache.current.delete(key);
        }
      }
    };

    return { setCache, getCache, clearCache, clearExpiredCache };
  }, [cacheExpiry]);

  const debounce = useCallback(<F extends (...args: any[]) => any>(
    func: F,
    key: string = 'default'
  ): F => {
    return ((...args: Parameters<F>) => {
      const existingTimer = debounceTimers.current.get(key);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }

      const timer = setTimeout(() => {
        func(...args);
        debounceTimers.current.delete(key);
      }, debounceDelay);

      debounceTimers.current.set(key, timer);
    }) as F;
  }, [debounceDelay]);

  return {
    ...memoizedCache,
    debounce
  };
}
