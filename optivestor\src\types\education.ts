
export interface Tutorial {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  completed: boolean;
}

export interface EducationModule {
  id: string;
  title: string;
  description: string;
  category: 'Market Basics' | 'Technical Analysis' | 'Risk Management' | 'Investment Strategies';
  progress: number;
  totalLessons: number;
  completedLessons: number;
  enrolledUsers: number;
}

export interface RiskGuide {
  id: string;
  title: string;
  description: string;
  riskLevel: 'Low' | 'Medium' | 'High';
  keyPoints: string[];
  readTime: string;
}

export interface InvestmentStrategy {
  id: string;
  title: string;
  description: string;
  strategyType: 'Conservative' | 'Moderate' | 'Aggressive' | 'Balanced';
  expectedReturn: string;
  timeHorizon: string;
  minimumInvestment: string;
  suitableFor: string[];
}
