import { useState, useEffect } from 'react';
import { Stock } from '@/utils/stockApi';

interface UseWatchlistOptions {
  initialStocks?: Stock[];
  maxItems?: number;
}

export const useWatchlist = (options: UseWatchlistOptions = {}) => {
  const { initialStocks = [], maxItems = 10 } = options;
  
  const [watchlistStocks, setWatchlistStocks] = useState<Stock[]>([]);

  // Initialize watchlist with provided stocks
  useEffect(() => {
    if (initialStocks && initialStocks.length > 0) {
      setWatchlistStocks(initialStocks.slice(0, maxItems));
    }
  }, [initialStocks, maxItems]);

  const addToWatchlist = (stock: Stock) => {
    try {
      setWatchlistStocks(prev => {
        // Check if stock already exists
        if (prev.some(s => s.symbol === stock.symbol)) {
          console.log(`${stock.symbol} is already in watchlist`);
          return prev;
        }
        
        // Add stock and respect max items limit
        const newWatchlist = [...prev, stock];
        return newWatchlist.slice(0, maxItems);
      });
    } catch (error) {
      console.error('Error adding to watchlist:', error);
    }
  };

  const removeFromWatchlist = (symbol: string) => {
    try {
      setWatchlistStocks(prev => prev.filter(stock => stock.symbol !== symbol));
    } catch (error) {
      console.error('Error removing from watchlist:', error);
    }
  };

  const isInWatchlist = (symbol: string): boolean => {
    return watchlistStocks.some(stock => stock.symbol === symbol);
  };

  const clearWatchlist = () => {
    try {
      setWatchlistStocks([]);
    } catch (error) {
      console.error('Error clearing watchlist:', error);
    }
  };

  return {
    watchlistStocks,
    addToWatchlist,
    removeFromWatchlist,
    isInWatchlist,
    clearWatchlist,
    watchlistCount: watchlistStocks.length
  };
};

export type { UseWatchlistOptions };
