
import { InvestmentStrategy } from '@/types/education';

export const strategiesData: InvestmentStrategy[] = [
  {
    id: 'value-investing',
    title: 'Value Investing',
    description: 'Warren Buffett-style investing focused on undervalued companies.',
    strategyType: 'Conservative',
    expectedReturn: '8-12% annually',
    timeHorizon: '5+ years',
    minimumInvestment: '$1,000',
    suitableFor: ['Beginners', 'Risk-averse investors', 'Long-term holders']
  },
  {
    id: 'growth-investing',
    title: 'Growth Investing',
    description: 'Focus on companies with high growth potential and strong fundamentals.',
    strategyType: 'Moderate',
    expectedReturn: '10-15% annually',
    timeHorizon: '3-7 years',
    minimumInvestment: '$2,500',
    suitableFor: ['Intermediate investors', 'Growth seekers', 'Tech enthusiasts']
  },
  {
    id: 'momentum-trading',
    title: 'Momentum Trading',
    description: 'Capitalize on trending stocks and market momentum.',
    strategyType: 'Aggressive',
    expectedReturn: '15-25% annually',
    timeHorizon: '6 months - 2 years',
    minimumInvestment: '$5,000',
    suitableFor: ['Experienced traders', 'Active investors', 'Risk tolerant']
  },
  {
    id: 'index-investing',
    title: 'Index Fund Investing',
    description: 'Low-cost, diversified approach through index funds and ETFs.',
    strategyType: 'Balanced',
    expectedReturn: '7-10% annually',
    timeHorizon: '10+ years',
    minimumInvestment: '$100',
    suitableFor: ['All investors', 'Passive investors', 'Cost-conscious']
  }
];
