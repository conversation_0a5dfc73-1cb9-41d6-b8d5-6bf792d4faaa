
"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'

type Theme = 'dark' | 'light'

interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
  toggleTheme: () => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

export function ThemeProvider({ 
  children, 
  defaultTheme = 'dark',
  storageKey = 'vite-ui-theme'
}: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(defaultTheme)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    
    // Get stored theme or use default
    const storedTheme = localStorage.getItem(storageKey) as Theme
    if (storedTheme && (storedTheme === 'light' || storedTheme === 'dark')) {
      setThemeState(storedTheme)
    }
  }, [storageKey])

  useEffect(() => {
    if (!mounted) return

    // Apply theme to document
    const root = document.documentElement
    root.classList.remove('light', 'dark')
    root.classList.add(theme)
    
    // Store theme
    localStorage.setItem(storageKey, theme)
  }, [theme, mounted, storageKey])

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme)
  }

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light')
  }

  const value: ThemeContextType = {
    theme,
    setTheme,
    toggleTheme
  }

  // Provide a default context value even when not mounted to prevent errors
  const defaultValue: ThemeContextType = {
    theme: defaultTheme,
    setTheme: () => {},
    toggleTheme: () => {}
  }

  return (
    <ThemeContext.Provider value={mounted ? value : defaultValue}>
      <div suppressHydrationWarning={!mounted}>
        {children}
      </div>
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)

  if (context === undefined) {
    // More descriptive error message
    throw new Error('useTheme must be used within a ThemeProvider. Make sure your component is wrapped with <ThemeProvider>.')
  }

  return context
}

export type { ThemeProviderProps }
