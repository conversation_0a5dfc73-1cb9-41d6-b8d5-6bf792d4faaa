
import { useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

export function useTutorialActions(
  setActiveTutorial: (id: string | null) => void,
  setCompletedTutorials: React.Dispatch<React.SetStateAction<Set<string>>>
) {
  const { toast } = useToast();

  const handleStartTutorial = useCallback((tutorialId: string) => {
    setActiveTutorial(tutorialId);
    console.log('Starting tutorial:', tutorialId);
  }, [setActiveTutorial]);

  const handleCompleteTutorial = useCallback((tutorialId: string) => {
    setCompletedTutorials(prev => new Set([...prev, tutorialId]));
    setActiveTutorial(null);
    
    toast({
      title: "Tutorial Completed!",
      description: "Great job! You've successfully completed the tutorial.",
    });
    
    console.log('Completed tutorial:', tutorialId);
  }, [setCompletedTutorials, setActiveTutorial, toast]);

  const handleExitTutorial = useCallback(() => {
    setActiveTutorial(null);
  }, [setActiveTutorial]);

  return {
    handleStartTutorial,
    handleCompleteTutorial,
    handleExitTutorial
  };
}
