
import React from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { StockNews } from '@/components/news/StockNews';
import { WatchlistCard } from '@/components/watchlist/WatchlistCard';
import { PredictionChart } from '@/components/predictions/PredictionChart';
import { MarketStats } from '@/components/dashboard/MarketStats';
import { TopMovers } from '@/components/dashboard/TopMovers';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { useDashboard } from '@/hooks/useDashboard';

const Index = () => {
  const {
    stocks,
    selectedStock,
    watchlistStocks,
    dashboardData,
    isLoading,
    error,
    handleSearch,
    handleAddToWatchlist,
    handleRemoveFromWatchlist,
    handleSelectWatchlistStock
  } = useDashboard();
  
  if (error) {
    return (
      <PageLayout title="Market Dashboard" onSearch={handleSearch}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-destructive mb-2">Error Loading Dashboard</h2>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </PageLayout>
    );
  }
  
  return (
    <PageLayout title="Market Dashboard" onSearch={handleSearch}>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Market Stats */}
        <div className="col-span-1 lg:col-span-4">
          <ErrorBoundary>
            <MarketStats 
              data={dashboardData?.marketStats || {
                totalMarketCap: "$0",
                tradingVolume: "$0",
                topGainer: { symbol: "-", name: "-", change: 0 },
                topLoser: { symbol: "-", name: "-", change: 0 }
              }}
              isLoading={isLoading}
              error={error}
            />
          </ErrorBoundary>
        </div>
        
        {/* Main Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Stock prediction chart */}
          <ErrorBoundary>
            {dashboardData && !isLoading && selectedStock ? (
              <PredictionChart
                predictions={dashboardData.predictions}
                historicalPrices={dashboardData.historicalPrices}
                symbol={selectedStock.symbol}
              />
            ) : (
              <div className="animate-pulse bg-muted rounded-lg h-[400px]" />
            )}
          </ErrorBoundary>
          
          {/* News feed */}
          <ErrorBoundary>
            {dashboardData && !isLoading ? (
              <StockNews news={dashboardData.news} />
            ) : (
              <div className="animate-pulse bg-muted rounded-lg h-[300px]" />
            )}
          </ErrorBoundary>
        </div>
        
        {/* Sidebar */}
        <div className="lg:col-span-1 space-y-6">
          {/* Watchlist */}
          <ErrorBoundary>
            <WatchlistCard 
              title="My Watchlist"
              stocks={watchlistStocks}
              onAddClick={handleAddToWatchlist}
              onRemoveStock={handleRemoveFromWatchlist}
              onSelectStock={handleSelectWatchlistStock}
            />
          </ErrorBoundary>
          
          {/* Top Movers */}
          <ErrorBoundary>
            <TopMovers 
              stocks={stocks}
              onStockSelect={handleSelectWatchlistStock}
              isLoading={isLoading}
              error={error}
              maxItems={5}
            />
          </ErrorBoundary>
        </div>
      </div>
    </PageLayout>
  );
};

export default Index;
