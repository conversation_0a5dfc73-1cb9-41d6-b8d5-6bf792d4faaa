
import { useState, useMemo } from 'react';
import { tutorialsData } from '@/data/education/tutorials';
import { modulesData } from '@/data/education/modules';
import { riskGuidesData } from '@/data/education/riskGuides';
import { strategiesData } from '@/data/education/strategies';
import { useEducationFilters } from './education/useEducationFilters';
import { useTutorialActions } from './education/useTutorialActions';
import { useEducationActions } from './education/useEducationActions';
import { Tutorial } from '@/types/education';

export function useEducation() {
  const [activeFilter, setActiveFilter] = useState('all');
  const [activeTutorial, setActiveTutorial] = useState<string | null>(null);
  const [completedTutorials, setCompletedTutorials] = useState<Set<string>>(new Set());

  // Transform tutorials data to include completed status
  const tutorials: Tutorial[] = useMemo(() => 
    tutorialsData.map(tutorial => ({
      ...tutorial,
      completed: completedTutorials.has(tutorial.id)
    })), [completedTutorials]
  );

  const modules = modulesData;
  const riskGuides = riskGuidesData;
  const strategies = strategiesData;

  // Use the smaller hooks
  const { filteredTutorials, filteredModules } = useEducationFilters(
    tutorials,
    modules,
    activeFilter
  );

  const {
    handleStartTutorial,
    handleCompleteTutorial,
    handleExitTutorial
  } = useTutorialActions(setActiveTutorial, setCompletedTutorials);

  const {
    isLoading,
    handleEnrollModule,
    handleReadGuide,
    handleLearnStrategy
  } = useEducationActions();

  return {
    // Data
    tutorials: filteredTutorials,
    modules: filteredModules,
    riskGuides,
    strategies,
    
    // State
    isLoading,
    activeFilter,
    activeTutorial,
    
    // Actions
    setActiveFilter,
    handleStartTutorial,
    handleCompleteTutorial,
    handleExitTutorial,
    handleEnrollModule,
    handleReadGuide,
    handleLearnStrategy
  };
}
