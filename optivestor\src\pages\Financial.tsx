
import React from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { FinancialMetrics } from '@/components/financials/FinancialMetrics';
import { RevenueChart } from '@/components/financials/RevenueChart';
import { MarginsChart } from '@/components/financials/MarginsChart';
import { CashFlowChart } from '@/components/financials/CashFlowChart';
import { BalanceSheetChart } from '@/components/financials/BalanceSheetChart';
import { PeerComparisonChart } from '@/components/financials/PeerComparisonChart';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useFinancialData } from '@/hooks/useFinancialData';
import { useChartData } from '@/hooks/useChartData';

const Financial = () => {
  const { 
    selectedStock, 
    financialData, 
    balanceSheetData, 
    cashFlowData, 
    handleSearch 
  } = useFinancialData();

  const {
    revenueData,
    marginData,
    cashFlowChartData,
    balanceSheetChartData,
    peerComparisonData
  } = useChartData(financialData, balanceSheetData, cashFlowData, selectedStock.symbol);
  
  return (
    <PageLayout title="Financial Analysis" onSearch={handleSearch}>
      <div className="mb-6">
        <h2 className="text-xl font-semibold">
          {selectedStock.name} ({selectedStock.symbol}) Financial Analysis
        </h2>
        <p className="text-muted-foreground">
          Detailed financial performance and valuation metrics
        </p>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        <FinancialMetrics
          symbol={selectedStock.symbol}
          financialData={financialData}
          balanceSheetData={balanceSheetData}
          cashFlowData={cashFlowData}
        />
        
        <Tabs defaultValue="revenue">
          <TabsList className="w-full grid grid-cols-4 mb-4">
            <TabsTrigger value="revenue">Revenue & Profit</TabsTrigger>
            <TabsTrigger value="margins">Margins</TabsTrigger>
            <TabsTrigger value="cashflow">Cash Flow</TabsTrigger>
            <TabsTrigger value="balance">Balance Sheet</TabsTrigger>
          </TabsList>
          
          <TabsContent value="revenue">
            <RevenueChart data={revenueData} />
          </TabsContent>
          
          <TabsContent value="margins">
            <MarginsChart data={marginData} />
          </TabsContent>
          
          <TabsContent value="cashflow">
            <CashFlowChart data={cashFlowChartData} />
          </TabsContent>
          
          <TabsContent value="balance">
            <BalanceSheetChart data={balanceSheetChartData} />
          </TabsContent>
        </Tabs>
        
        <PeerComparisonChart 
          data={peerComparisonData} 
          stockSymbol={selectedStock.symbol}
        />
      </div>
    </PageLayout>
  );
};

export default Financial;
